import { useQuery } from "@tanstack/react-query";
import { AlertCircle, Calculator, Package, Settings } from "lucide-react";
import { useState } from "react";
import { useService } from "~/config/context/serviceProvider";
import { categoryOptions } from "~/modules/category/hooks/category-options";
import { CategoryCode } from "~/modules/category/service/model/category";
import { productOptions } from "~/modules/product/hooks/product-options";
import type { Product } from "~/modules/product/service/model/product";
import useRecipes from "../hooks/use-recipes";
import type { Recipe } from "../service/model/recipe";

interface MaterialRequirement {
	productId: string;
	productName: string;
	quantityPerBatch?: number;
	quantityPerUnit?: number;
	totalQuantity: number;
	measurementUnit: string;
	category: CategoryCode;
}

interface RecipeCalculation {
	recipe: Recipe;
	batchesNeeded: number;
	actualProduction: number;
	materialRequirements: MaterialRequirement[];
}

interface ProductionCalculation {
	targetProduct: Product;
	targetQuantity: number;
	applicableRecipes: Recipe[];
	selectedRecipe?: Recipe;
	recipeCalculations: RecipeCalculation[];
	productionInfoMaterials: MaterialRequirement[];
}

export default function ProductionCalculator() {
	const service = useService();
	const [targetProductId, setTargetProductId] = useState<string>("");
	const [targetQuantity, setTargetQuantity] = useState<number>(0);
	const [selectedRecipeId, setSelectedRecipeId] = useState<string>("");
	const [calculation, setCalculation] = useState<ProductionCalculation | null>(
		null,
	);

	// Fetch data
	const { data: allProducts = [] } = useQuery(productOptions(service));
	const { data: recipes = [] } = useRecipes();
	const { data: categories = [] } = useQuery(categoryOptions(service));

	// Filter products that can be produced (have recipes or production info)
	const producibleProducts = allProducts.filter((product) => {
		const hasRecipe = recipes.some((recipe) =>
			recipe.products.some((p) => p.id === product.id),
		);
		const hasProductionInfo = !!product.productionInfo;
		return hasRecipe || hasProductionInfo;
	});

	const getCategoryFromProduct = (product: Product): CategoryCode => {
		// Find the first category that matches known category codes
		const categoryMap = new Map(categories.map((c) => [c.id, c]));
		for (const categoryId of product.categoryIDs) {
			const category = categoryMap.get(categoryId);
			if (category) {
				const code = category.code as CategoryCode;
				if (
					[
						CategoryCode.SUPPLIERS,
						CategoryCode.MATERIALS,
						CategoryCode.RAW_MATERIALS,
					].includes(code)
				) {
					return code;
				}
			}
		}
		return CategoryCode.MATERIALS;
	};

	const calculateProductionRequirements = (): ProductionCalculation | null => {
		const targetProduct = allProducts.find((p) => p.id === targetProductId);
		if (!targetProduct || targetQuantity <= 0) return null;

		// Find applicable recipes
		const applicableRecipes = recipes.filter((recipe) =>
			recipe.products.some((product) => product.id === targetProductId),
		);

		// Calculate requirements for each recipe
		const recipeCalculations: RecipeCalculation[] = applicableRecipes.map(
			(recipe) => {
				const batchesNeeded = Math.ceil(targetQuantity / recipe.batchSize);
				const actualProduction = batchesNeeded * recipe.batchSize;

				const materialRequirements: MaterialRequirement[] =
					recipe.components.map((component) => ({
						productId: component.product.id,
						productName: component.product.name,
						quantityPerBatch: component.quantity,
						totalQuantity: component.quantity * batchesNeeded,
						measurementUnit: component.product.measurementUnitID || "",
						category: getCategoryFromProduct(component.product),
					}));

				return {
					recipe,
					batchesNeeded,
					actualProduction,
					materialRequirements,
				};
			},
		);

		// Handle production info materials
		let productionInfoMaterials: MaterialRequirement[] = [];
		if (targetProduct.productionInfo?.materials) {
			productionInfoMaterials = targetProduct.productionInfo.materials.map(
				(material) => {
					const materialProduct = allProducts.find(
						(p) => p.id === material.productId,
					);
					return {
						productId: material.productId,
						productName: materialProduct?.name || "Unknown",
						quantityPerUnit: material.quantity,
						totalQuantity: material.quantity * targetQuantity,
						measurementUnit: materialProduct?.measurementUnitID || "",
						category: getCategoryFromProduct(
							materialProduct || ({} as Product),
						),
					};
				},
			);
		}

		return {
			targetProduct,
			targetQuantity,
			applicableRecipes,
			selectedRecipe: selectedRecipeId
				? applicableRecipes.find((r) => r.id === selectedRecipeId)
				: applicableRecipes[0],
			recipeCalculations,
			productionInfoMaterials,
		};
	};

	const handleCalculate = () => {
		const result = calculateProductionRequirements();
		setCalculation(result);
	};

	const selectedCalculation =
		calculation?.recipeCalculations.find(
			(calc) => calc.recipe.id === selectedRecipeId,
		) || calculation?.recipeCalculations[0];

	return (
		<div className="space-y-6">
			{/* Input Section */}
			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<h2 className="card-title">
						<Calculator size={20} />
						Calculadora de Producción
					</h2>
					<p className="text-base-content/70">
						Calcule los materiales necesarios para producir una cantidad
						específica de producto
					</p>

					<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
						<div>
							<label className="label" htmlFor="target-product">
								<span className="label-text">Producto a Producir</span>
							</label>
							<select
								id="target-product"
								className="select select-bordered w-full"
								value={targetProductId}
								onChange={(e) => {
									setTargetProductId(e.target.value);
									setSelectedRecipeId("");
									setCalculation(null);
								}}
							>
								<option value="">Seleccionar producto...</option>
								{producibleProducts.map((product) => (
									<option key={product.id} value={product.id}>
										{product.name} ({product.code})
									</option>
								))}
							</select>
						</div>

						<div>
							<label className="label" htmlFor="target-quantity">
								<span className="label-text">Cantidad Objetivo</span>
							</label>
							<input
								id="target-quantity"
								type="number"
								className="input input-bordered w-full"
								value={targetQuantity || ""}
								onChange={(e) => setTargetQuantity(Number(e.target.value))}
								min="1"
								step="1"
							/>
						</div>

						<div className="flex items-end">
							<button
								type="button"
								onClick={handleCalculate}
								className="btn btn-primary w-full"
								disabled={!targetProductId || targetQuantity <= 0}
							>
								<Calculator size={16} />
								Calcular
							</button>
						</div>
					</div>
				</div>
			</div>

			{/* Recipe Selection */}
			{calculation && calculation.applicableRecipes.length > 1 && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h3 className="card-title">
							<Settings size={20} />
							Selección de Receta
						</h3>
						<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
							{calculation.recipeCalculations.map((calc) => (
								<div
									key={calc.recipe.id}
									className={`card cursor-pointer border-2 transition-colors ${
										selectedRecipeId === calc.recipe.id ||
										(
											!selectedRecipeId &&
												calc === calculation.recipeCalculations[0]
										)
											? "border-primary bg-primary/10"
											: "border-base-300 hover:border-primary/50"
									}`}
									onClick={() => setSelectedRecipeId(calc.recipe.id)}
									onKeyDown={(e) => {
										if (e.key === "Enter" || e.key === " ") {
											e.preventDefault();
											setSelectedRecipeId(calc.recipe.id);
										}
									}}
									tabIndex={0}
									role="button"
									aria-label={`Seleccionar receta ${calc.recipe.name}`}
								>
									<div className="card-body p-4">
										<h4 className="font-semibold">{calc.recipe.name}</h4>
										<p className="text-base-content/70 text-sm">
											Código: {calc.recipe.code}
										</p>
										<div className="text-sm">
											<div>Tamaño de lote: {calc.recipe.batchSize}</div>
											<div>Lotes necesarios: {calc.batchesNeeded}</div>
											<div>Producción real: {calc.actualProduction}</div>
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			)}

			{/* Results */}
			{calculation && selectedCalculation && (
				<div className="space-y-6">
					{/* Production Summary */}
					<div className="card bg-base-100 shadow-xl">
						<div className="card-body">
							<h3 className="card-title">
								<Package size={20} />
								Resumen de Producción
							</h3>
							<div className="grid grid-cols-2 gap-4 md:grid-cols-4">
								<div className="stat">
									<div className="stat-title">Producto</div>
									<div className="stat-value text-lg">
										{calculation.targetProduct.name}
									</div>
								</div>
								<div className="stat">
									<div className="stat-title">Cantidad Objetivo</div>
									<div className="stat-value text-lg">
										{calculation.targetQuantity}
									</div>
								</div>
								<div className="stat">
									<div className="stat-title">Lotes Necesarios</div>
									<div className="stat-value text-lg">
										{selectedCalculation.batchesNeeded}
									</div>
								</div>
								<div className="stat">
									<div className="stat-title">Producción Real</div>
									<div className="stat-value text-lg">
										{selectedCalculation.actualProduction}
									</div>
								</div>
							</div>
							{selectedCalculation.actualProduction >
								calculation.targetQuantity && (
								<div className="alert alert-info">
									<AlertCircle size={16} />
									<span>
										Se producirán{" "}
										{selectedCalculation.actualProduction -
											calculation.targetQuantity}{" "}
										unidades adicionales debido al tamaño de lote mínimo.
									</span>
								</div>
							)}
						</div>
					</div>

					{/* Recipe Components */}
					{selectedCalculation.materialRequirements.length > 0 && (
						<MaterialsCard
							title="Componentes de Receta"
							materials={selectedCalculation.materialRequirements}
							showPerBatch={true}
							bgColor="bg-blue-50"
							borderColor="border-blue-200"
						/>
					)}

					{/* Production Info Materials */}
					{calculation.productionInfoMaterials.length > 0 && (
						<MaterialsCard
							title="Materiales de Producción"
							materials={calculation.productionInfoMaterials}
							showPerBatch={false}
							bgColor="bg-green-50"
							borderColor="border-green-200"
						/>
					)}
				</div>
			)}
		</div>
	);
}

interface MaterialsCardProps {
	title: string;
	materials: MaterialRequirement[];
	showPerBatch: boolean;
	bgColor: string;
	borderColor: string;
}

function MaterialsCard({
	title,
	materials,
	showPerBatch,
	bgColor,
	borderColor,
}: MaterialsCardProps) {
	// Group materials by category
	const groupedMaterials = materials.reduce(
		(acc, material) => {
			const category = material.category;
			if (!acc[category]) acc[category] = [];
			acc[category].push(material);
			return acc;
		},
		{} as Record<CategoryCode, MaterialRequirement[]>,
	);

	const categoryNames = {
		[CategoryCode.SUPPLIERS]: "Insumos",
		[CategoryCode.MATERIALS]: "Materiales",
		[CategoryCode.RAW_MATERIALS]: "Materias Primas",
	};

	return (
		<div className={`card border-2 bg-base-100 shadow-xl ${borderColor}`}>
			<div className="card-body">
				<h3 className="card-title">{title}</h3>
				{Object.entries(groupedMaterials).map(
					([category, categoryMaterials]) => (
						<div key={category} className="space-y-2">
							<h4 className="font-semibold text-lg">
								{categoryNames[category as CategoryCode]}
							</h4>
							{categoryMaterials.map((material) => (
								<div
									key={material.productId}
									className={`rounded-lg p-3 ${bgColor}`}
								>
									<div className="font-medium">{material.productName}</div>
									{showPerBatch && material.quantityPerBatch && (
										<div className="text-base-content/70 text-sm">
											Por lote: {material.quantityPerBatch}{" "}
											{material.measurementUnit}
										</div>
									)}
									{!showPerBatch && material.quantityPerUnit && (
										<div className="text-base-content/70 text-sm">
											Por unidad: {material.quantityPerUnit}{" "}
											{material.measurementUnit}
										</div>
									)}
									<div className="font-semibold text-sm">
										Total: {material.totalQuantity.toFixed(2)}{" "}
										{material.measurementUnit}
									</div>
								</div>
							))}
						</div>
					),
				)}
			</div>
		</div>
	);
}

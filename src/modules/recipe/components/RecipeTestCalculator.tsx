import { useQuery } from "@tanstack/react-query";
import { Calculator, Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import { useService } from "~/config/context/serviceProvider";
import { categoryOptions } from "~/modules/category/hooks/category-options";
import { CategoryCode } from "~/modules/category/service/model/category";
import { productOptions } from "~/modules/product/hooks/product-options";
import useProductsByCategory from "~/modules/product/hooks/use-products-by-category";
import useRecipes from "../hooks/use-recipes";
import {
	type MaterialRequirement,
	type ProductQuantityInput,
	type RecipeCalculationResult,
	calculateMaterialRequirements,
} from "../utils/recipeCalculations";

interface ProductInput {
	id: string;
	productId: string;
	quantity: number;
}

export default function RecipeTestCalculator() {
	const service = useService();
	const [productInputs, setProductInputs] = useState<ProductInput[]>([]);
	const [calculationResult, setCalculationResult] =
		useState<RecipeCalculationResult | null>(null);

	// Fetch data
	const { data: products = [] } = useProductsByCategory(CategoryCode.PRODUCTS);
	const { data: allProducts = [] } = useQuery(productOptions(service)); // Fetch all products for materials lookup
	const { data: recipes = [] } = useRecipes();
	const { data: categories = [] } = useQuery(categoryOptions(service));

	const addProductInput = () => {
		const newInput: ProductInput = {
			id: `input-${Date.now()}-${Math.random()}`,
			productId: "",
			quantity: 0,
		};
		setProductInputs([...productInputs, newInput]);
	};

	const updateProductInput = (
		id: string,
		updates: Partial<Omit<ProductInput, "id">>,
	) => {
		setProductInputs((prev) =>
			prev.map((input) => (input.id === id ? { ...input, ...updates } : input)),
		);
	};

	const removeProductInput = (id: string) => {
		setProductInputs((prev) => prev.filter((input) => input.id !== id));
	};

	const calculateRequirements = () => {
		console.log(productInputs);
		const validInputs: ProductQuantityInput[] = productInputs
			.filter((input) => input.productId && input.quantity > 0)
			.map((input) => ({
				productId: input.productId,
				quantity: input.quantity,
			}));

		if (validInputs.length === 0) {
			alert("Por favor, agregue al menos un producto con cantidad válida");
			return;
		}

		console.log("Starting calculation with:", {
			validInputs,
			productsCount: products.length,
			allProductsCount: allProducts.length,
			recipesCount: recipes.length,
			categoriesCount: categories.length,
		});

		console.log("Products data:", products);
		console.log("All products data:", allProducts);
		console.log("Categories data:", categories);

		// Check if selected products have production info
		validInputs.forEach((input) => {
			const product = allProducts.find((p) => p.id === input.productId);
			console.log(`Selected product ${product?.name}:`, {
				hasProductionInfo: !!product?.productionInfo,
				productionInfo: product?.productionInfo,
				categoryIDs: product?.categoryIDs,
			});
		});

		const result = calculateMaterialRequirements(
			validInputs,
			allProducts, // Use all products for material lookup
			recipes,
			categories,
		);

		console.log("Calculation result:", result);
		setCalculationResult(result);
	};

	const getProductName = (productId: string) => {
		const product = allProducts.find((p) => p.id === productId);
		return product ? `${product.name} (${product.code})` : "";
	};

	return (
		<div className="space-y-6">
			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<h2 className="card-title">
						<Calculator size={20} />
						Simulador de Cálculo de Recetas
					</h2>
					<p className="text-base-content/70">
						Seleccione productos y cantidades para calcular los materiales
						necesarios
					</p>

					<div className="alert alert-info">
						<div>
							<h4 className="font-semibold">Requisitos para el cálculo:</h4>
							<ul className="mt-2 list-inside list-disc text-sm">
								<li>
									Los productos deben tener información de producción
									configurada
								</li>
								<li>
									Los materiales deben estar categorizados como "Insumos",
									"Materiales" o "Materias Primas"
								</li>
								<li>
									Para productos BULK: se requiere cantidad por unidad y lista
									de materiales
								</li>
								<li>
									Para productos UNIT: se requiere lista de materiales por
									unidad
								</li>
							</ul>
						</div>
					</div>

					<div className="space-y-4">
						<div className="flex items-center justify-between">
							<h3 className="font-semibold">Productos a Producir</h3>
							<div className="flex gap-2">
								<button
									type="button"
									onClick={() => {
										console.log("Debug info:", {
											products: products.length,
											allProducts: allProducts.length,
											categories: categories.length,
											productsWithProductionInfo: allProducts.filter(
												(p) => p.productionInfo,
											).length,
										});
									}}
									className="btn btn-secondary btn-sm"
								>
									Debug Info
								</button>
								<button
									type="button"
									onClick={addProductInput}
									className="btn btn-primary btn-sm"
								>
									<Plus size={16} />
									Agregar Producto
								</button>
							</div>
						</div>

						{productInputs.length === 0 && (
							<div className="py-8 text-center text-base-content/50">
								No hay productos agregados. Haga clic en "Agregar Producto" para
								comenzar.
							</div>
						)}

						{productInputs.map((input) => (
							<div key={input.id} className="flex items-end gap-4">
								<div className="flex-1">
									<label className="label" htmlFor={`product-${input.id}`}>
										<span className="label-text">Producto</span>
									</label>
									<select
										id={`product-${input.id}`}
										className="select select-bordered w-full"
										value={input.productId}
										onChange={(e) =>
											updateProductInput(input.id, {
												productId: e.target.value,
											})
										}
									>
										<option value="">Seleccionar producto...</option>
										{products.map((product) => (
											<option key={product.id} value={product.id}>
												{product.name} ({product.code})
											</option>
										))}
									</select>
								</div>

								<div className="w-32">
									<label className="label" htmlFor={`quantity-${input.id}`}>
										<span className="label-text">Cantidad</span>
									</label>
									<input
										id={`quantity-${input.id}`}
										type="number"
										className="input input-bordered w-full"
										value={input.quantity || ""}
										onChange={(e) =>
											updateProductInput(input.id, {
												quantity: Number(e.target.value),
											})
										}
										min="0"
										step="0.01"
									/>
								</div>

								<button
									type="button"
									onClick={() => removeProductInput(input.id)}
									className="btn btn-error btn-sm"
								>
									<Trash2 size={16} />
								</button>
							</div>
						))}

						{productInputs.length > 0 && (
							<div className="flex justify-end">
								<button
									type="button"
									onClick={calculateRequirements}
									className="btn btn-primary"
								>
									<Calculator size={16} />
									Calcular Materiales
								</button>
							</div>
						)}
					</div>
				</div>
			</div>

			{calculationResult && (
				<div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
					<MaterialRequirementsCard
						title="Insumos"
						materials={calculationResult.suppliers}
						getProductName={getProductName}
						bgColor="bg-blue-50"
						borderColor="border-blue-200"
					/>
					<MaterialRequirementsCard
						title="Materiales"
						materials={calculationResult.materials}
						getProductName={getProductName}
						bgColor="bg-green-50"
						borderColor="border-green-200"
					/>
					<MaterialRequirementsCard
						title="Materias Primas"
						materials={calculationResult.rawMaterials}
						getProductName={getProductName}
						bgColor="bg-orange-50"
						borderColor="border-orange-200"
					/>
				</div>
			)}

			{calculationResult && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h3 className="card-title">Resumen de Costos</h3>
						<div className="stat">
							<div className="stat-title">Costo Total Estimado</div>
							<div className="stat-value text-primary">
								${calculationResult.totalCost.toFixed(2)}
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}

interface MaterialRequirementsCardProps {
	title: string;
	materials: MaterialRequirement[];
	getProductName: (productId: string) => string;
	bgColor: string;
	borderColor: string;
}

function MaterialRequirementsCard({
	title,
	materials,
	getProductName,
	bgColor,
	borderColor,
}: MaterialRequirementsCardProps) {
	return (
		<div className={`card border-2 bg-base-100 shadow-xl ${borderColor}`}>
			<div className="card-body">
				<h3 className="card-title">{title}</h3>
				{materials.length === 0 ? (
					<p className="text-base-content/50">
						No se requieren {title.toLowerCase()}
					</p>
				) : (
					<div className="space-y-2">
						{materials.map((material) => (
							<div
								key={material.productId}
								className={`rounded-lg p-3 ${bgColor}`}
							>
								<div className="font-medium">{material.productName}</div>
								<div className="text-base-content/70 text-sm">
									Código: {material.productCode}
								</div>
								<div className="font-semibold text-sm">
									Cantidad: {material.totalQuantity.toFixed(2)}
									{material.unitName && ` ${material.unitName}`}
								</div>
							</div>
						))}
					</div>
				)}
			</div>
		</div>
	);
}

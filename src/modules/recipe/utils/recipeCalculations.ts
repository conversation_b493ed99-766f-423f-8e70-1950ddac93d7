import type { Category } from "~/modules/category/service/model/category";
import type { Product } from "~/modules/product/service/model/product";
import type { Recipe } from "../service/model/recipe";

export interface ProductQuantityInput {
	productId: string;
	quantity: number;
}

export interface MaterialRequirement {
	productId: string;
	productName: string;
	productCode: string;
	categoryCode: string;
	totalQuantity: number;
	unitName?: string;
}

export interface RecipeCalculationResult {
	suppliers: MaterialRequirement[];
	materials: MaterialRequirement[];
	rawMaterials: MaterialRequirement[];
	totalCost: number;
}

/**
 * Calculate material requirements based on product production info and recipes
 */
export function calculateMaterialRequirements(
	productInputs: ProductQuantityInput[],
	products: Product[],
	recipes: Recipe[],
	categories: Category[],
): RecipeCalculationResult {
	const suppliers: MaterialRequirement[] = [];
	const materials: MaterialRequirement[] = [];
	const rawMaterials: MaterialRequirement[] = [];
	let totalCost = 0;

	// Create maps for quick lookup
	const productMap = new Map(products.map((p) => [p.id, p]));
	const categoryMap = new Map(categories.map((c) => [c.id, c]));

	for (const input of productInputs) {
		const product = productMap.get(input.productId);
		if (!product) {
			console.warn(`Product not found: ${input.productId}`);
			continue;
		}

		const requestedQuantity = input.quantity;
		let materialsProcessed = false;

		// First, try to find a recipe for this product
		const productRecipes = recipes.filter((recipe) =>
			recipe.products.some((p) => p.id === product.id),
		);

		if (productRecipes.length > 0) {
			// Use recipe-based calculation
			const recipe = productRecipes[0]; // Use the first recipe found
			console.log(
				`Using recipe ${recipe.name} (type: ${recipe.type}) for product ${product.name}`,
			);

			// Calculate how many batches we need based on recipe batch size
			const batchesNeeded = Math.ceil(requestedQuantity / recipe.batchSize);
			console.log(
				`Batches needed: ${batchesNeeded} (${requestedQuantity} units / ${recipe.batchSize} batch size)`,
			);

			if (recipe.type === "UNIT") {
				// For UNIT recipes: use recipe components
				console.log(
					`UNIT recipe: using ${recipe.components.length} recipe components`,
				);
				for (const component of recipe.components) {
					// Convert recipe component product to full product structure
					const componentProduct: Product = {
						...component.product,
						productionInfo: null, // Recipe components don't have production info
					};
					const totalComponentQuantity = component.quantity * batchesNeeded;

					addMaterialRequirement(
						componentProduct,
						totalComponentQuantity,
						suppliers,
						materials,
						rawMaterials,
						categoryMap,
					);
					totalCost +=
						(componentProduct.costPrice || 0) * totalComponentQuantity;
				}
			} else if (recipe.type === "BULK") {
				// For BULK recipes: calculate BOTH recipe components AND production info materials
				console.log(
					"BULK recipe: calculating recipe components AND production info materials",
				);

				// 1. Calculate recipe components (for the recipe content itself)
				console.log(`Processing ${recipe.components.length} recipe components`);
				for (const component of recipe.components) {
					const componentProduct: Product = {
						...component.product,
						productionInfo: null,
					};
					const totalComponentQuantity = component.quantity * batchesNeeded;

					addMaterialRequirement(
						componentProduct,
						totalComponentQuantity,
						suppliers,
						materials,
						rawMaterials,
						categoryMap,
					);
					totalCost +=
						(componentProduct.costPrice || 0) * totalComponentQuantity;
				}

				// 2. Calculate production info materials (for packaging, containers, etc.)
				if (product.productionInfo?.materials) {
					const unitQuantity = product.productionInfo.unitQuantity || 1;
					const totalUnitsInBatches = batchesNeeded * recipe.batchSize;
					const materialMultiplier = totalUnitsInBatches / unitQuantity;

					console.log(
						`Production info materials calculation: ${totalUnitsInBatches} total units / ${unitQuantity} unit quantity = ${materialMultiplier} multiplier`,
					);

					for (const material of product.productionInfo.materials) {
						const materialProduct = productMap.get(material.productId);
						if (materialProduct) {
							const totalMaterialQuantity =
								material.quantity * materialMultiplier;
							addMaterialRequirement(
								materialProduct,
								totalMaterialQuantity,
								suppliers,
								materials,
								rawMaterials,
								categoryMap,
							);
							totalCost +=
								(materialProduct.costPrice || 0) * totalMaterialQuantity;
						} else {
							console.warn(`Material product not found: ${material.productId}`);
						}
					}
				} else {
					console.warn(
						`BULK recipe but product ${product.name} has no production info materials`,
					);
				}
			}
			materialsProcessed = true;
		}

		// If no recipe found, try production info materials
		if (!materialsProcessed && product.productionInfo) {
			console.log(`Using production info for product ${product.name}`);
			const { productionInfo } = product;

			if (productionInfo.productionType === "UNIT") {
				// For UNIT production, calculate based on materials in production info
				// Each unit requires the specified materials
				for (const material of productionInfo.materials) {
					const materialProduct = productMap.get(material.productId);
					if (materialProduct) {
						const totalMaterialQuantity = material.quantity * requestedQuantity;
						addMaterialRequirement(
							materialProduct,
							totalMaterialQuantity,
							suppliers,
							materials,
							rawMaterials,
							categoryMap,
						);
						totalCost +=
							(materialProduct.costPrice || 0) * totalMaterialQuantity;
					} else {
						console.warn(`Material product not found: ${material.productId}`);
					}
				}
			} else if (productionInfo.productionType === "BULK") {
				// For BULK production, calculate based on unit quantity and materials
				const unitQuantity = productionInfo.unitQuantity || 1;
				const unitsNeeded = requestedQuantity / unitQuantity;

				// Calculate materials from production info
				for (const material of productionInfo.materials) {
					const materialProduct = productMap.get(material.productId);
					if (materialProduct) {
						const totalMaterialQuantity = material.quantity * unitsNeeded;
						addMaterialRequirement(
							materialProduct,
							totalMaterialQuantity,
							suppliers,
							materials,
							rawMaterials,
							categoryMap,
						);
						totalCost +=
							(materialProduct.costPrice || 0) * totalMaterialQuantity;
					} else {
						console.warn(`Material product not found: ${material.productId}`);
					}
				}
			}
			materialsProcessed = true;
		}

		if (!materialsProcessed) {
			console.warn(`Product ${product.name} has no recipe or production info`);
		}
	}

	const result = {
		suppliers: consolidateMaterials(suppliers),
		materials: consolidateMaterials(materials),
		rawMaterials: consolidateMaterials(rawMaterials),
		totalCost,
	};

	console.log("Calculation summary:", {
		suppliersCount: result.suppliers.length,
		materialsCount: result.materials.length,
		rawMaterialsCount: result.rawMaterials.length,
		totalCost: result.totalCost,
	});

	return result;
}

/**
 * Add material requirement to the appropriate category based on product category
 */
function addMaterialRequirement(
	product: Product,
	quantity: number,
	suppliers: MaterialRequirement[],
	materials: MaterialRequirement[],
	rawMaterials: MaterialRequirement[],
	categoryMap: Map<string, Category>,
) {
	const categoryCode = getCategoryCode(product, categoryMap);
	const requirement: MaterialRequirement = {
		productId: product.id,
		productName: product.name,
		productCode: product.code,
		categoryCode,
		totalQuantity: quantity,
		unitName: product.measurementUnitID, // This should be resolved to unit name in UI
	};

	// Categorize based on product category
	if (categoryCode === "SUPPLIERS") {
		suppliers.push(requirement);
	} else if (categoryCode === "MATERIALS") {
		materials.push(requirement);
	} else if (
		categoryCode === "RAW-MATERIALS" ||
		categoryCode === "RAW_MATERIALS"
	) {
		rawMaterials.push(requirement);
	}
}

/**
 * Get the primary category code for a product
 */
function getCategoryCode(
	product: Product,
	categoryMap: Map<string, Category>,
): string {
	// Find the first category that matches known category codes
	for (const categoryId of product.categoryIDs) {
		const category = categoryMap.get(categoryId);
		if (category) {
			const code = category.code;
			if (
				["SUPPLIERS", "MATERIALS", "RAW_MATERIALS", "RAW-MATERIALS"].includes(
					code,
				)
			) {
				return code;
			}
		}
	}
	// Default to MATERIALS if no specific category found
	return "MATERIALS";
}

/**
 * Consolidate materials with the same product ID
 */
function consolidateMaterials(
	materials: MaterialRequirement[],
): MaterialRequirement[] {
	const consolidated = new Map<string, MaterialRequirement>();

	for (const material of materials) {
		const existing = consolidated.get(material.productId);
		if (existing) {
			existing.totalQuantity += material.totalQuantity;
		} else {
			consolidated.set(material.productId, { ...material });
		}
	}

	return Array.from(consolidated.values());
}

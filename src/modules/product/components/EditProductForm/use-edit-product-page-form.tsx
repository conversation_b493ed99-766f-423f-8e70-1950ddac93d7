import { useNavigate } from "@tanstack/react-router";
import { useStore } from "@tanstack/react-store";
import { toast } from "react-toastify";
import { useService } from "~/config/context/serviceProvider";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useUpdateProduct from "../../hooks/use-update-product";
import type { Product } from "../../service/model/product";
import { productTabStore } from "../../store/createProductTab";
import { UpdateProductSchema } from "./schema";

interface UseEditProductFormProps {
	product: Product;
}

export default function useEditProductForm({
	product,
}: UseEditProductFormProps) {
	const navigate = useNavigate();
	const service = useService();
	const { mutate, isPending } = useUpdateProduct();

	const selectedTab = useStore(productTabStore);

	const form = useAppForm({
		defaultValues: {
			name: product.name,
			commercialName: product.commercialName,
			code: product.code,
			skuCode: product.skuCode,
			brandID: product.brandID,
			measurementUnitID: product.measurementUnitID,
			categoryIDs: product.categoryIDs,
			state: product.state,
			description: product.description || "",
			canBeSold: product.canBeSold,
			canBePurchased: product.canBePurchased,
			costPrice: product.costPrice,
			costPriceTotal: product.costPriceTotal,
			productionInfo: product.productionInfo || {
				productionType: "",
				unitQuantity: null,
				measurementUnitID: null,
				materials: [],
			},
		} as UpdateProductSchema,
		validators: {
			onChange: UpdateProductSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: product.id,
					name: value.name,
					commercialName: value.commercialName,
					code: value.code,
					skuCode: value.skuCode,
					brandID: value.brandID,
					measurementUnitID: value.measurementUnitID,
					categoryIDs: value.categoryIDs,
					state: value.state,
					description: value.description || undefined,
					canBeSold: value.canBeSold,
					canBePurchased: value.canBePurchased,
					costPrice: value.costPrice,
					costPriceTotal: value.costPriceTotal,
					productionInfo: value.productionInfo || undefined,
				},
				{
					onSuccess: () => {
						toast.success("Producto actualizado exitosamente");
						navigate({ to: "/admin/products/products" });
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	return {
		form,
		isPending,
		selectedTab,
	};
}
